/* CSS Custom Properties */
:root {
    /* Colors */
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #66ffdd;
    --accent-secondary: #6670ff;
    --text-primary: #000000;
    --text-secondary: #4d4d4d;
    --text-light: #808080;
    --background-primary: #ffffff;
    --background-secondary: #fafafa;
    --background-tertiary: #f2f2f2;
    --border-color: #e6e6e6;
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-serif: 'PT Serif', Georgia, serif;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;
    
    /* Layout */
    --container-max-width: 1200px;
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
    font-size: 16px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

.btn-primary:hover {
    background-color: var(--text-secondary);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: var(--spacing-sm) 0;
    transition: var(--transition);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo .logo {
    height: 32px;
    width: auto;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.nav-link {
    font-weight: 500;
    color: var(--text-secondary);
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle .bar {
    width: 24px;
    height: 2px;
    background-color: var(--primary-color);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-xxl) 0;
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
}

.hero-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.hero-title {
    font-family: var(--font-serif);
    font-style: italic;
    margin-bottom: var(--spacing-md);
}

.hero-title-line {
    display: block;
    animation: fadeInUp 0.8s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.hero-title-line:nth-child(2) {
    animation-delay: 0.2s;
}

.hero-description {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xl);
    animation: fadeInUp 0.8s ease 0.4s forwards;
    opacity: 0;
    transform: translateY(30px);
}

.hero-cta {
    display: flex;
    gap: var(--spacing-md);
    animation: fadeInUp 0.8s ease 0.6s forwards;
    opacity: 0;
    transform: translateY(30px);
}

.hero-image {
    position: relative;
}

.hero-img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    animation: fadeInRight 0.8s ease 0.3s forwards;
    opacity: 0;
    transform: translateX(30px);
}

/* Section Styles */
section {
    padding: var(--spacing-xxl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.section-title {
    margin-bottom: var(--spacing-md);
}

.section-description {
    font-size: 1.125rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Work Section */
.work {
    background-color: var(--background-secondary);
}

.work-filter {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border-color: var(--primary-color);
}

.work-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.work-item {
    background-color: var(--background-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    cursor: pointer;
}

.work-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.work-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.work-item-content {
    padding: var(--spacing-md);
}

.work-item-title {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xs);
}

.work-item-category {
    color: var(--text-light);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.about-img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Contact Section */
.contact {
    background-color: var(--background-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
}

.contact-details {
    margin-top: var(--spacing-xl);
}

.contact-item {
    display: flex;
    flex-direction: column;
    margin-bottom: var(--spacing-md);
}

.contact-label {
    font-size: 0.875rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: var(--spacing-xs);
}

.contact-link {
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--primary-color);
}

.contact-link:hover {
    color: var(--accent-secondary);
}

/* Form Styles */
.form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-input {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
    font-family: var(--font-primary);
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: var(--spacing-xxl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.footer-logo .logo {
    height: 32px;
    width: auto;
    filter: invert(1);
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
}

.footer-link {
    color: var(--secondary-color);
    opacity: 0.8;
    transition: var(--transition);
}

.footer-link:hover {
    opacity: 1;
}

.footer-social {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

.social-link {
    color: var(--secondary-color);
    opacity: 0.8;
    transition: var(--transition);
}

.social-link:hover {
    opacity: 1;
    color: var(--accent-color);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-text {
    opacity: 0.6;
    margin: 0;
}

/* Animations */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes revealUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Loading and reveal animations */
.animate-in {
    animation: revealUp 0.8s ease forwards;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease forwards;
}

/* Enhanced hover effects */
.work-item {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.work-item:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.work-item:hover img {
    transform: scale(1.1);
    transition: transform 0.4s ease;
}

.work-item img {
    transition: transform 0.4s ease;
}

/* Button hover animations */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Navbar scroll effect */
.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* Stats counter animation */
.stat-number {
    transition: all 0.3s ease;
}

.stat:hover .stat-number {
    transform: scale(1.1);
    color: var(--accent-secondary);
}

/* Form focus animations */
.form-input {
    position: relative;
}

.form-input:focus {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Mobile menu animation */
.nav-menu {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nav-toggle .bar {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: var(--background-primary);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: var(--spacing-xl);
        transition: var(--transition);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-list {
        flex-direction: column;
        gap: var(--spacing-xl);
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-cta {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .about-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-lg);
    }
    
    .footer-links,
    .footer-social {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .work-grid {
        grid-template-columns: 1fr;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
    }
}
