// Portfolio data
const portfolioData = [
    {
        id: 1,
        title: "E-commerce Platform",
        category: "web",
        image: "assets/images/project-1.jpg",
        description: "Modern e-commerce platform with seamless user experience"
    },
    {
        id: 2,
        title: "Brand Identity Design",
        category: "branding",
        image: "assets/images/project-2.jpg",
        description: "Complete brand identity system for tech startup"
    },
    {
        id: 3,
        title: "Mobile Banking App",
        category: "mobile",
        image: "assets/images/project-3.jpg",
        description: "Intuitive mobile banking application design"
    },
    {
        id: 4,
        title: "Corporate Website",
        category: "web",
        image: "assets/images/project-4.jpg",
        description: "Professional corporate website with modern design"
    },
    {
        id: 5,
        title: "Restaurant Branding",
        category: "branding",
        image: "assets/images/project-1.jpg",
        description: "Complete branding package for upscale restaurant"
    },
    {
        id: 6,
        title: "Fitness App UI",
        category: "mobile",
        image: "assets/images/project-2.jpg",
        description: "User-friendly fitness tracking application"
    }
];

// DOM Elements
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');
const navLinks = document.querySelectorAll('.nav-link');
const filterBtns = document.querySelectorAll('.filter-btn');
const workGrid = document.querySelector('.work-grid');
const form = document.querySelector('.form');

// Initialize the website
document.addEventListener('DOMContentLoaded', function() {
    initNavigation();
    initPortfolioFilter();
    initAnimations();
    initForm();
    populatePortfolio();
    initScrollEffects();
    initCounterAnimation();
});

// Navigation functionality
function initNavigation() {
    // Mobile menu toggle
    navToggle.addEventListener('click', () => {
        navMenu.classList.toggle('active');
        
        // Animate hamburger menu
        const bars = navToggle.querySelectorAll('.bar');
        bars.forEach((bar, index) => {
            if (navMenu.classList.contains('active')) {
                if (index === 0) bar.style.transform = 'rotate(45deg) translate(5px, 5px)';
                if (index === 1) bar.style.opacity = '0';
                if (index === 2) bar.style.transform = 'rotate(-45deg) translate(7px, -6px)';
            } else {
                bar.style.transform = 'none';
                bar.style.opacity = '1';
            }
        });
    });

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
            
            // Close mobile menu
            navMenu.classList.remove('active');
            const bars = navToggle.querySelectorAll('.bar');
            bars.forEach(bar => {
                bar.style.transform = 'none';
                bar.style.opacity = '1';
            });
            
            // Update active link
            navLinks.forEach(navLink => navLink.classList.remove('active'));
            link.classList.add('active');
        });
    });

    // Update active nav link on scroll
    window.addEventListener('scroll', updateActiveNavLink);
}

// Update active navigation link based on scroll position
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const scrollPos = window.scrollY + 100;

    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');
        const navLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);

        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
            navLinks.forEach(link => link.classList.remove('active'));
            if (navLink) navLink.classList.add('active');
        }
    });
}

// Portfolio filter functionality
function initPortfolioFilter() {
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const filter = btn.getAttribute('data-filter');
            
            // Update active filter button
            filterBtns.forEach(filterBtn => filterBtn.classList.remove('active'));
            btn.classList.add('active');
            
            // Filter portfolio items
            filterPortfolio(filter);
        });
    });
}

// Filter portfolio items
function filterPortfolio(filter) {
    const filteredData = filter === 'all' 
        ? portfolioData 
        : portfolioData.filter(item => item.category === filter);
    
    // Animate out current items
    const currentItems = workGrid.querySelectorAll('.work-item');
    currentItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
        }, index * 50);
    });
    
    // Replace with filtered items after animation
    setTimeout(() => {
        renderPortfolio(filteredData);
    }, currentItems.length * 50 + 200);
}

// Populate portfolio grid
function populatePortfolio() {
    renderPortfolio(portfolioData);
}

// Render portfolio items
function renderPortfolio(data) {
    workGrid.innerHTML = '';
    
    data.forEach((item, index) => {
        const workItem = createWorkItem(item);
        workGrid.appendChild(workItem);
        
        // Animate in new items
        setTimeout(() => {
            workItem.style.opacity = '1';
            workItem.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Create work item element
function createWorkItem(item) {
    const workItem = document.createElement('div');
    workItem.className = 'work-item';
    workItem.style.opacity = '0';
    workItem.style.transform = 'translateY(20px)';
    workItem.style.transition = 'all 0.6s ease';
    
    workItem.innerHTML = `
        <img src="${item.image}" alt="${item.title}" loading="lazy">
        <div class="work-item-content">
            <h3 class="work-item-title">${item.title}</h3>
            <p class="work-item-category">${item.category}</p>
        </div>
    `;
    
    // Add click event for potential modal or detail view
    workItem.addEventListener('click', () => {
        console.log('Clicked on:', item.title);
        // You can add modal functionality here
    });
    
    return workItem;
}

// Initialize scroll animations
function initAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.section-header, .about-content, .contact-content, .work-item');
    animateElements.forEach(el => {
        observer.observe(el);
    });
    
    // Add parallax effect to hero image
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const heroImg = document.querySelector('.hero-img');
        if (heroImg) {
            heroImg.style.transform = `translateY(${scrolled * 0.1}px)`;
        }
    });
}

// Form handling
function initForm() {
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
}

// Handle form submission
function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(form);
    const name = form.querySelector('input[type="text"]').value;
    const email = form.querySelector('input[type="email"]').value;
    const message = form.querySelector('textarea').value;
    
    // Simple validation
    if (!name || !email || !message) {
        showNotification('Please fill in all fields', 'error');
        return;
    }
    
    // Simulate form submission
    const submitBtn = form.querySelector('.btn');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        showNotification('Message sent successfully!', 'success');
        form.reset();
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '15px 20px',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        backgroundColor: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'
    });
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add smooth reveal animations for elements
function addRevealAnimation() {
    const style = document.createElement('style');
    style.textContent = `
        .animate-in {
            animation: revealUp 0.8s ease forwards;
        }
        
        @keyframes revealUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .work-item {
            transition: all 0.3s ease;
        }
        
        .work-item:hover {
            transform: translateY(-10px);
        }
        
        .hero-title-line {
            animation: fadeInUp 0.8s ease forwards;
        }
        
        .hero-title-line:nth-child(2) {
            animation-delay: 0.2s;
        }
    `;
    document.head.appendChild(style);
}

// Initialize reveal animations
addRevealAnimation();

// Add loading animation
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
});

// Smooth scrolling for all anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Initialize scroll effects
function initScrollEffects() {
    const navbar = document.querySelector('.navbar');

    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
}

// Initialize counter animation
function initCounterAnimation() {
    const counters = document.querySelectorAll('.stat-number');
    const observerOptions = {
        threshold: 0.5
    };

    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.textContent);
                animateCounter(counter, target);
                counterObserver.unobserve(counter);
            }
        });
    }, observerOptions);

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// Animate counter numbers
function animateCounter(element, target) {
    let current = 0;
    const increment = target / 50;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            element.textContent = target + '+';
            clearInterval(timer);
        } else {
            element.textContent = Math.floor(current) + '+';
        }
    }, 30);
}

// Add typing effect to hero title
function initTypingEffect() {
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        const lines = heroTitle.querySelectorAll('.hero-title-line');
        lines.forEach((line, index) => {
            const text = line.textContent;
            line.textContent = '';
            line.style.opacity = '1';

            setTimeout(() => {
                typeText(line, text, 100);
            }, index * 1000);
        });
    }
}

// Type text effect
function typeText(element, text, speed) {
    let i = 0;
    const timer = setInterval(() => {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
        } else {
            clearInterval(timer);
        }
    }, speed);
}

// Initialize typing effect after page load
window.addEventListener('load', () => {
    setTimeout(initTypingEffect, 500);
});
